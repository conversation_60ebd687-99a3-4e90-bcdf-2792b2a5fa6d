'use client'

import React, { useState, useRef, useEffect } from 'react'

interface SimpleBubbleTextEditorProps {
  bubbleId: string
  x: number
  y: number
  width: number
  height: number
  initialText: string
  isEditing: boolean
  onTextChange: (text: string) => void
  onStartEdit: () => void
  onStopEdit: () => void
  fontSize?: number
  fontFamily?: string
  textAlign?: 'left' | 'center' | 'right'
}

/**
 * Éditeur de texte simple pour les bulles de dialogue
 * Version temporaire en attendant l'intégration complète de Canvas Editor
 */
export const SimpleBubbleTextEditor: React.FC<SimpleBubbleTextEditorProps> = ({
  bubbleId,
  x,
  y,
  width,
  height,
  initialText,
  isEditing,
  onTextChange,
  onStartEdit,
  onStopEdit,
  fontSize = 16,
  fontFamily = 'Arial',
  textAlign = 'center'
}) => {
  const [text, setText] = useState(initialText)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Focus automatique quand on entre en mode édition
  useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.focus()
      textareaRef.current.select()
    }
  }, [isEditing])

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value
    setText(newText)
    onTextChange(newText)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onStopEdit()
    } else if (e.key === 'Enter' && e.ctrlKey) {
      onStopEdit()
    }
  }

  const handleBlur = () => {
    onStopEdit()
  }

  const handleDoubleClick = () => {
    if (!isEditing) {
      onStartEdit()
    }
  }

  // Calculer la position avec un petit padding
  const padding = 10
  const editorStyle: React.CSSProperties = {
    position: 'absolute',
    left: x + padding,
    top: y + padding,
    width: width - (padding * 2),
    height: height - (padding * 2),
    fontSize: `${fontSize}px`,
    fontFamily,
    textAlign,
    border: 'none',
    outline: 'none',
    background: 'transparent',
    resize: 'none',
    overflow: 'hidden',
    zIndex: isEditing ? 1000 : 1,
    pointerEvents: isEditing ? 'auto' : 'none',
    cursor: isEditing ? 'text' : 'pointer',
    color: '#000000',
    lineHeight: '1.2'
  }

  const displayStyle: React.CSSProperties = {
    position: 'absolute',
    left: x + padding,
    top: y + padding,
    width: width - (padding * 2),
    height: height - (padding * 2),
    fontSize: `${fontSize}px`,
    fontFamily,
    textAlign,
    display: 'flex',
    alignItems: 'center',
    justifyContent: textAlign === 'center' ? 'center' : textAlign === 'right' ? 'flex-end' : 'flex-start',
    color: '#000000',
    lineHeight: '1.2',
    wordWrap: 'break-word',
    overflow: 'hidden',
    zIndex: 1,
    pointerEvents: 'none',
    whiteSpace: 'pre-wrap'
  }

  return (
    <>
      {isEditing ? (
        <textarea
          ref={textareaRef}
          value={text}
          onChange={handleTextChange}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
          style={editorStyle}
          placeholder="Tapez votre texte ici..."
        />
      ) : (
        <div
          style={displayStyle}
          onDoubleClick={handleDoubleClick}
        >
          {text || 'Double-cliquez pour éditer'}
        </div>
      )}
    </>
  )
}
