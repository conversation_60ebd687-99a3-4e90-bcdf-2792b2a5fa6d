// ✅ CORRECTION SSR : Import dynamique sécurisé avec lazy loading
let Editor: any = null
let isEditorLoading = false

const loadCanvasEditor = async (): Promise<any> => {
  if (Editor) return Editor
  if (isEditorLoading) return null
  if (typeof window === 'undefined') return null

  try {
    isEditorLoading = true
    const module = await import('@hufe921/canvas-editor')
    Editor = module.default
    console.log('✅ Canvas Editor loaded successfully')
    return Editor
  } catch (error) {
    console.error('❌ Failed to load Canvas Editor:', error)
    return null
  } finally {
    isEditorLoading = false
  }
}

export interface SpeechBubbleConfig {
  id: string
  x: number
  y: number
  width: number
  height: number
  shape: 'oval' | 'rectangular' | 'cloud' | 'thought'
  content: string
  fontSize?: number
  fontFamily?: string
  textAlign?: 'left' | 'center' | 'right'
  backgroundColor?: string
  borderColor?: string
  borderWidth?: number
}

export interface SpeechBubbleEvents {
  onContentChange?: (content: string) => void
  onFocus?: () => void
  onBlur?: () => void
  onResize?: (width: number, height: number) => void
}

/**
 * ⚠️ SYSTÈME OBSOLÈTE - REMPLACÉ PAR KonvaSpeechBubbleUnified
 *
 * Ce système Canvas Editor est désormais obsolète et remplacé par
 * le système Konva unifié qui offre de meilleures performances
 * et une intégration native avec le canvas.
 *
 * @deprecated Utiliser KonvaSpeechBubbleUnified à la place
 */
export class SpeechBubbleCanvasEditor {
  private canvasEditor: Editor | null = null
  private container: HTMLDivElement | null = null
  private config: SpeechBubbleConfig
  private events: SpeechBubbleEvents
  private isInitialized = false

  constructor(config: SpeechBubbleConfig, events: SpeechBubbleEvents = {}) {
    this.config = config
    this.events = events
  }

  /**
   * ✅ CORRECTION : Initialisation asynchrone sécurisée avec Canvas Editor
   */
  async initialize(parentContainer: HTMLElement): Promise<void> {
    if (this.isInitialized || typeof window === 'undefined') return

    // ✅ Charger Canvas Editor de manière asynchrone
    const EditorClass = await loadCanvasEditor()
    if (!EditorClass) {
      console.warn('⚠️ Canvas Editor non disponible, fallback vers système HTML')
      return
    }

    // Créer le container pour Canvas Editor
    this.container = document.createElement('div')
    this.container.id = `bubble-editor-${this.config.id}`
    this.container.style.cssText = `
      position: absolute;
      left: ${this.config.x}px;
      top: ${this.config.y}px;
      width: ${this.config.width}px;
      height: ${this.config.height}px;
      z-index: 1000;
      pointer-events: auto;
      background: transparent;
      border-radius: ${this.getBorderRadius()}px;
      overflow: hidden;
    `

    parentContainer.appendChild(this.container)

    try {
      // Initialiser Canvas Editor
      this.canvasEditor = new EditorClass(
        this.container,
        this.getInitialContent(),
        this.getEditorOptions()
      )

      // Configurer les événements
      this.setupEventListeners()
      this.isInitialized = true
      console.log('✅ Canvas Editor initialisé avec succès')
    } catch (error) {
      console.error('❌ Erreur initialisation Canvas Editor:', error)
      this.cleanup()
    }
  }

  /**
   * Active le mode édition
   */
  startEditing(): void {
    if (!this.canvasEditor || !this.container) return

    this.container.style.pointerEvents = 'auto'
    this.canvasEditor.command.executeSetPositionContext({ startIndex: 0, endIndex: 0 })
    this.events.onFocus?.()
  }

  /**
   * Désactive le mode édition
   */
  stopEditing(): void {
    if (!this.canvasEditor || !this.container) return

    this.container.style.pointerEvents = 'none'
    this.events.onBlur?.()
  }

  /**
   * Met à jour la position et taille de la bulle
   */
  updateTransform(x: number, y: number, width: number, height: number): void {
    if (!this.container) return

    this.config.x = x
    this.config.y = y
    this.config.width = width
    this.config.height = height

    this.container.style.left = `${x}px`
    this.container.style.top = `${y}px`
    this.container.style.width = `${width}px`
    this.container.style.height = `${height}px`
  }

  /**
   * Obtient le contenu texte actuel
   */
  getContent(): string {
    if (!this.canvasEditor) return this.config.content

    // Utiliser l'API Canvas Editor pour extraire le texte
    const elements = this.canvasEditor.command.getValue()
    return elements.map((el: any) => el.value || '').join('')
  }

  /**
   * Met à jour le contenu
   */
  setContent(content: string): void {
    if (!this.canvasEditor) {
      this.config.content = content
      return
    }

    const elements = content.split('\n').map(line => ({ value: line }))
    this.canvasEditor.command.executeReplaceValue(elements)
  }

  /**
   * ✅ NETTOYAGE SÉCURISÉ : Méthode de nettoyage interne
   */
  private cleanup(): void {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container)
    }
    this.container = null
    this.isInitialized = false
  }

  /**
   * ✅ DESTRUCTION COMPLÈTE : Détruit l'éditeur et nettoie les ressources
   */
  destroy(): void {
    if (this.canvasEditor) {
      try {
        // Détruire l'instance Canvas Editor si elle a une méthode destroy
        if (typeof this.canvasEditor.destroy === 'function') {
          this.canvasEditor.destroy()
        }
      } catch (error) {
        console.warn('⚠️ Erreur lors de la destruction Canvas Editor:', error)
      }
    }
    this.canvasEditor = null
    this.cleanup()
  }

  /**
   * Vérifie si l'éditeur est en mode édition
   */
  isEditing(): boolean {
    return this.container?.style.pointerEvents === 'auto'
  }

  // Méthodes privées

  private getInitialContent(): any[] {
    const lines = this.config.content.split('\n')
    return lines.map(line => ({
      value: line || ' ', // Espace pour les lignes vides
      size: this.config.fontSize || 16,
      font: this.config.fontFamily || 'Arial',
      color: this.config.borderColor || '#000000'
    }))
  }

  private getEditorOptions(): any {
    return {
      width: this.config.width - 20, // Marge intérieure
      height: this.config.height - 20,
      defaultFont: this.config.fontFamily || 'Arial',
      defaultSize: this.config.fontSize || 16,
      defaultColor: this.config.borderColor || '#000000',
      margins: [10, 10, 10, 10], // Marges intérieures
      pageMode: 'continuous', // Mode continu pour les bulles
      renderMode: 'speed', // Performance optimisée
      // Désactiver les éléments non nécessaires pour les bulles
      header: { disabled: true },
      footer: { disabled: true },
      pageNumber: { disabled: true },
      // Style de la bulle
      background: {
        color: this.config.backgroundColor || 'transparent'
      }
    }
  }

  private getBorderRadius(): number {
    switch (this.config.shape) {
      case 'oval':
      case 'thought':
        return Math.min(this.config.width, this.config.height) / 2
      case 'cloud':
        return 20
      case 'rectangular':
      default:
        return 10
    }
  }

  private setupEventListeners(): void {
    if (!this.canvasEditor) return

    // Écouter les changements de contenu
    this.canvasEditor.listener.contentChange = () => {
      const content = this.getContent()
      this.events.onContentChange?.(content)
    }

    // Écouter les événements de focus/blur
    this.canvasEditor.listener.focus = () => {
      this.events.onFocus?.()
    }

    this.canvasEditor.listener.blur = () => {
      this.events.onBlur?.()
    }
  }
}
