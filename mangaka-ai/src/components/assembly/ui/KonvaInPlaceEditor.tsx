'use client'

// KonvaInPlaceEditor - Éditeur de texte IN-PLACE intégré directement dans la bulle
// AUCUN overlay externe, édition directement dans la zone de texte de la bulle

import React, { useEffect, useRef, useState, useCallback } from 'react'
import { DialogueElement } from '../types/assembly.types'

interface KonvaInPlaceEditorProps {
  element: DialogueElement
  isEditing: boolean
  onUpdate: (elementId: string, updates: Partial<DialogueElement>) => void
  onFinishEdit: () => void
  stageRef?: React.RefObject<any>
  bubblePosition: { x: number; y: number; width: number; height: number }
}

export default function KonvaInPlaceEditor({
  element,
  isEditing,
  onUpdate,
  onFinishEdit,
  stageRef,
  bubblePosition
}: KonvaInPlaceEditorProps) {
  const inputRef = useRef<HTMLTextAreaElement>(null)
  const [position, setPosition] = useState({ x: 0, y: 0, width: 0, height: 0 })
  const [text, setText] = useState(element.text || '')

  // ✅ CALCUL DE POSITION EXACTE SUR LA ZONE DE TEXTE DE LA BULLE
  useEffect(() => {
    if (!isEditing || !stageRef?.current) return

    const stage = stageRef.current
    const stageContainer = stage.container()
    const stageRect = stageContainer.getBoundingClientRect()

    // Position exacte de la zone de texte dans la bulle (avec padding)
    const textPadding = 10
    const textX = bubblePosition.x + textPadding
    const textY = bubblePosition.y + textPadding
    const textWidth = bubblePosition.width - (textPadding * 2)
    const textHeight = bubblePosition.height - (textPadding * 2)

    // Conversion en coordonnées écran absolues
    const screenX = stageRect.left + textX
    const screenY = stageRect.top + textY

    setPosition({
      x: screenX,
      y: screenY,
      width: textWidth,
      height: textHeight
    })
  }, [isEditing, bubblePosition, stageRef])

  // ✅ FOCUS AUTOMATIQUE ET SÉLECTION DU TEXTE
  useEffect(() => {
    if (isEditing && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus()
        inputRef.current?.select()
      }, 50)
    }
  }, [isEditing])

  // ✅ GESTION CLAVIER : ENTER pour valider, ESC pour annuler
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleFinishEdit()
    } else if (e.key === 'Escape') {
      e.preventDefault()
      // Annuler les changements
      setText(element.text || '')
      onFinishEdit()
    }
  }, [element.text, onFinishEdit])

  // ✅ MISE À JOUR EN TEMPS RÉEL
  const handleTextChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value
    setText(newText)
    
    // Mise à jour immédiate de l'élément
    onUpdate(element.id, { text: newText })
  }, [element.id, onUpdate])

  // ✅ FINALISATION DE L'ÉDITION
  const handleFinishEdit = useCallback(() => {
    onUpdate(element.id, { text })
    onFinishEdit()
  }, [element.id, text, onUpdate, onFinishEdit])

  // ✅ FERMETURE AU CLIC EXTÉRIEUR
  const handleBlur = useCallback(() => {
    handleFinishEdit()
  }, [handleFinishEdit])

  if (!isEditing) return null

  return (
    <textarea
      ref={inputRef}
      value={text}
      onChange={handleTextChange}
      onKeyDown={handleKeyDown}
      onBlur={handleBlur}
      className="fixed z-50 resize-none"
      style={{
        left: position.x,
        top: position.y,
        width: position.width,
        height: position.height,
        fontSize: `${element.dialogueStyle.fontSize}px`,
        fontFamily: element.dialogueStyle.fontFamily,
        color: `#${element.dialogueStyle.textColor.toString(16).padStart(6, '0')}`,
        textAlign: element.dialogueStyle.textAlign,
        padding: '4px',
        margin: 0,
        outline: 'none',
        border: 'none',
        borderRadius: '0',
        backgroundColor: 'transparent',
        lineHeight: '1.4',
        overflow: 'hidden',
        boxShadow: 'none',
        // ✅ INVISIBILITÉ TOTALE - Aucun élément visible
        background: 'transparent',
        resize: 'none',
        // ✅ CURSEUR VISIBLE MAIS CADRE INVISIBLE
        caretColor: '#000000', // Curseur visible
        // ✅ SÉLECTION VISIBLE
        '::selection': {
          backgroundColor: 'rgba(59, 130, 246, 0.3)'
        }
      }}
      placeholder=""
    />
  )
}
