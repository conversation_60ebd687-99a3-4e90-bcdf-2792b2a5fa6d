'use client'

// TipTapCanvasEditor - Éditeur TipTap intégré pour SimpleCanvasEditor
// Compatible avec le système Canvas HTML5 existant

import React, { useEffect, useRef, useCallback, useState } from 'react'
import ReactDOM from 'react-dom'
import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'

interface CanvasElement {
  id: string
  type: 'panel' | 'bubble' | 'image' | 'text'
  x: number
  y: number
  width: number
  height: number
  textContent?: string
  style?: any
}

interface TipTapCanvasEditorProps {
  element: CanvasElement
  isEditing: boolean
  onUpdate: (elementId: string, updates: Partial<CanvasElement>) => void
  onFinishEdit: () => void
  canvasContainer: HTMLElement
  canvasScale?: number
}

export default function TipTapCanvasEditor({
  element,
  isEditing,
  onUpdate,
  onFinishEdit,
  canvasContainer,
  canvasScale = 1
}: TipTapCanvasEditorProps) {
  const editorContainerRef = useRef<HTMLDivElement>(null)
  const resizeObserverRef = useRef<ResizeObserver | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)

  // ✅ CONFIGURATION TIPTAP OPTIMISÉE POUR CANVAS
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        // Fonctionnalités minimales pour speech bubbles
        heading: false,
        blockquote: false,
        codeBlock: false,
        horizontalRule: false,
        listItem: false,
        orderedList: false,
        bulletList: false,
      }),
      Placeholder.configure({
        placeholder: 'Tapez votre texte...',
      }),
    ],
    content: element.textContent || '',
    immediatelyRender: false,
    shouldRerenderOnTransaction: false,
    editorProps: {
      attributes: {
        class: 'tiptap-canvas-editor',
        style: 'outline: none; border: none; padding: 0; margin: 0; width: 100%; height: 100%;'
      },
    },
    onUpdate: ({ editor }) => {
      const newText = editor.getText()
      onUpdate(element.id, { textContent: newText })
    },
    onBlur: () => {
      onFinishEdit()
    },
  })

  // ✅ CRÉATION DU CONTAINER POUR CANVAS HTML5
  const createEditorContainer = useCallback(() => {
    if (!canvasContainer || !isEditing) return

    // Créer le container de l'éditeur
    const container = document.createElement('div')
    container.className = 'tiptap-canvas-container'
    
    // ✅ POSITIONNEMENT RELATIF AU CANVAS
    const canvasRect = canvasContainer.getBoundingClientRect()
    const padding = 8
    
    container.style.cssText = `
      position: absolute;
      left: ${element.x + padding}px;
      top: ${element.y + padding}px;
      width: ${element.width - (padding * 2)}px;
      min-height: ${Math.max(40, element.height - (padding * 2))}px;
      z-index: 1000;
      background: rgba(255, 255, 255, 0.95);
      border: 2px solid #3b82f6;
      border-radius: 6px;
      padding: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      font-family: Arial, sans-serif;
      font-size: 16px;
      color: #000000;
      text-align: center;
      line-height: 1.4;
      overflow: hidden;
      resize: none;
      transition: all 0.2s ease;
      transform: scale(${canvasScale});
      transform-origin: top left;
    `

    // Ajouter au container Canvas
    canvasContainer.appendChild(container)
    editorContainerRef.current = container
    setIsInitialized(true)

    console.log('✅ TipTapCanvasEditor: Container créé', {
      elementId: element.id,
      position: { x: element.x, y: element.y },
      size: { width: element.width, height: element.height }
    })

    return container
  }, [canvasContainer, isEditing, element, canvasScale])

  // ✅ RESIZEOBSERVER POUR AUTO-REDIMENSIONNEMENT
  const setupResizeObserver = useCallback(() => {
    if (!editorContainerRef.current) return

    resizeObserverRef.current = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { height } = entry.contentRect
        const minBubbleHeight = 60
        const padding = 20
        const newBubbleHeight = Math.max(minBubbleHeight, height + padding)
        
        // ✅ ADAPTATION INTELLIGENTE de la bulle au contenu
        onUpdate(element.id, {
          height: newBubbleHeight
        })
      }
    })

    resizeObserverRef.current.observe(editorContainerRef.current)
  }, [element.id, onUpdate])

  // ✅ INITIALISATION QUAND L'ÉDITION COMMENCE
  useEffect(() => {
    console.log('🔍 TipTapCanvasEditor useEffect:', {
      isEditing,
      isInitialized,
      hasEditor: !!editor,
      elementId: element.id
    })
    
    if (isEditing && !isInitialized) {
      console.log('🔍 TipTapCanvasEditor: Création du container...')
      const container = createEditorContainer()
      if (container && editor) {
        console.log('✅ TipTapCanvasEditor: Container créé, initialisation...')
        // Attendre que le DOM soit prêt
        setTimeout(() => {
          setupResizeObserver()
          editor.commands.focus()
          editor.commands.selectAll()
          console.log('✅ TipTapCanvasEditor: Éditeur focalisé et texte sélectionné')
        }, 50)
      } else {
        console.log('❌ TipTapCanvasEditor: Échec création container ou éditeur manquant')
      }
    }
  }, [isEditing, isInitialized, createEditorContainer, setupResizeObserver, editor])

  // ✅ NETTOYAGE QUAND L'ÉDITION SE TERMINE
  useEffect(() => {
    if (!isEditing && isInitialized) {
      // Nettoyer le ResizeObserver
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect()
        resizeObserverRef.current = null
      }

      // Supprimer le container de l'éditeur
      if (editorContainerRef.current && canvasContainer.contains(editorContainerRef.current)) {
        canvasContainer.removeChild(editorContainerRef.current)
      }
      
      editorContainerRef.current = null
      setIsInitialized(false)
      
      console.log('🧹 TipTapCanvasEditor: Nettoyage terminé')
    }
  }, [isEditing, isInitialized, canvasContainer])

  // ✅ GESTION DES ÉVÉNEMENTS CLAVIER
  useEffect(() => {
    if (!isEditing || !editor) return

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        e.preventDefault()
        onFinishEdit()
      }
      // Enter sans Shift pour valider
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault()
        onFinishEdit()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isEditing, editor, onFinishEdit])

  // ✅ NETTOYAGE FINAL
  useEffect(() => {
    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect()
      }
      if (editorContainerRef.current && canvasContainer?.contains(editorContainerRef.current)) {
        canvasContainer.removeChild(editorContainerRef.current)
      }
    }
  }, [canvasContainer])

  // ✅ RENDU CONDITIONNEL
  if (!isEditing || !isInitialized || !editorContainerRef.current) {
    return null
  }

  // ✅ PORTAL VERS LE CONTAINER CANVAS CRÉÉ
  return ReactDOM.createPortal(
    <EditorContent editor={editor} />,
    editorContainerRef.current
  )
}
