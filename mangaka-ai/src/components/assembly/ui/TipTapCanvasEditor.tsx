'use client'

// TipTapCanvasEditor - Éditeur TipTap intégré pour SimpleCanvasEditor
// Compatible avec le système Canvas HTML5 existant

import React, { useEffect, useRef, useCallback, useState } from 'react'
import ReactDOM from 'react-dom'
import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'

interface CanvasElement {
  id: string
  type: 'panel' | 'bubble' | 'image' | 'text'
  x: number
  y: number
  width: number
  height: number
  textContent?: string
  style?: any
}

interface TipTapCanvasEditorProps {
  element: CanvasElement
  isEditing: boolean
  onUpdate: (elementId: string, updates: Partial<CanvasElement>) => void
  onFinishEdit: () => void
  canvasContainer: HTMLElement
  canvasScale?: number
}

export default function TipTapCanvasEditor({
  element,
  isEditing,
  onUpdate,
  onFinishEdit,
  canvasContainer,
  canvasScale = 1
}: TipTapCanvasEditorProps) {
  const editorContainerRef = useRef<HTMLDivElement>(null)
  const resizeObserverRef = useRef<ResizeObserver | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)

  // ✅ CONFIGURATION TIPTAP OPTIMISÉE POUR CANVAS
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        // Fonctionnalités minimales pour speech bubbles
        heading: false,
        blockquote: false,
        codeBlock: false,
        horizontalRule: false,
        listItem: false,
        orderedList: false,
        bulletList: false,
      }),
      Placeholder.configure({
        placeholder: 'Tapez votre texte...',
      }),
    ],
    content: element.textContent || '',
    immediatelyRender: false,
    shouldRerenderOnTransaction: false,
    editorProps: {
      attributes: {
        class: 'tiptap-canvas-editor',
        style: 'outline: none; border: none; padding: 0; margin: 0; width: 100%; height: 100%;'
      },
    },
    onUpdate: ({ editor }) => {
      const newText = editor.getText()
      onUpdate(element.id, { textContent: newText })
    },
    onBlur: () => {
      onFinishEdit()
    },
  })

  // ✅ CRÉATION DU CONTAINER UNIFIÉ POUR CANVAS HTML5
  const createEditorContainer = useCallback(() => {
    if (!canvasContainer || !isEditing) return

    // Créer le container de l'éditeur
    const container = document.createElement('div')
    container.className = 'tiptap-canvas-container'

    // ✅ POSITIONNEMENT EXACT RELATIF AU CANVAS
    const canvasRect = canvasContainer.getBoundingClientRect()
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop

    // ✅ CALCUL PRÉCIS DES COORDONNÉES
    const absoluteX = canvasRect.left + scrollLeft + element.x
    const absoluteY = canvasRect.top + scrollTop + element.y
    const padding = 8

    container.style.cssText = `
      position: absolute;
      left: ${absoluteX + padding}px;
      top: ${absoluteY + padding}px;
      width: ${element.width - (padding * 2)}px;
      min-height: ${Math.max(40, element.height - (padding * 2))}px;
      z-index: 1000;
      background: rgba(255, 255, 255, 0.95);
      border: 2px solid #3b82f6;
      border-radius: 6px;
      padding: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      font-family: Arial, sans-serif;
      font-size: 16px;
      color: #000000;
      text-align: center;
      line-height: 1.4;
      overflow: hidden;
      resize: none;
      transition: all 0.2s ease;
      transform: scale(${canvasScale});
      transform-origin: top left;
    `

    // Ajouter au document body pour positionnement absolu correct
    document.body.appendChild(container)
    editorContainerRef.current = container
    setIsInitialized(true)

    console.log('✅ TipTapCanvasEditor: Container créé avec positionnement exact', {
      elementId: element.id,
      canvasRect,
      elementPosition: { x: element.x, y: element.y },
      absolutePosition: { x: absoluteX, y: absoluteY },
      size: { width: element.width, height: element.height }
    })

    return container
  }, [canvasContainer, isEditing, element, canvasScale])

  // ✅ SUPPRIMÉ : ResizeObserver causait une boucle infinie
  // L'auto-redimensionnement sera géré différemment pour éviter les boucles de rendu

  // ✅ INITIALISATION QUAND L'ÉDITION COMMENCE
  useEffect(() => {
    console.log('🔍 TipTapCanvasEditor useEffect:', {
      isEditing,
      isInitialized,
      hasEditor: !!editor,
      elementId: element.id
    })
    
    if (isEditing && !isInitialized) {
      console.log('🔍 TipTapCanvasEditor: Création du container...')
      const container = createEditorContainer()
      if (container && editor) {
        console.log('✅ TipTapCanvasEditor: Container créé, initialisation...')
        // Attendre que le DOM soit prêt
        setTimeout(() => {
          editor.commands.focus()
          editor.commands.selectAll()
          console.log('✅ TipTapCanvasEditor: Éditeur focalisé et texte sélectionné')
        }, 50)
      } else {
        console.log('❌ TipTapCanvasEditor: Échec création container ou éditeur manquant')
      }
    }
  }, [isEditing, isInitialized, createEditorContainer, editor])

  // ✅ NETTOYAGE QUAND L'ÉDITION SE TERMINE
  useEffect(() => {
    if (!isEditing && isInitialized) {
      // Nettoyer le ResizeObserver
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect()
        resizeObserverRef.current = null
      }

      // Supprimer le container de l'éditeur du document body
      if (editorContainerRef.current && document.body.contains(editorContainerRef.current)) {
        document.body.removeChild(editorContainerRef.current)
      }
      
      editorContainerRef.current = null
      setIsInitialized(false)
      
      console.log('🧹 TipTapCanvasEditor: Nettoyage terminé')
    }
  }, [isEditing, isInitialized, canvasContainer])

  // ✅ GESTION DES ÉVÉNEMENTS CLAVIER
  useEffect(() => {
    if (!isEditing || !editor) return

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        e.preventDefault()
        onFinishEdit()
      }
      // Enter sans Shift pour valider
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault()
        onFinishEdit()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isEditing, editor, onFinishEdit])

  // ✅ NETTOYAGE FINAL
  useEffect(() => {
    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect()
      }
      if (editorContainerRef.current && document.body.contains(editorContainerRef.current)) {
        document.body.removeChild(editorContainerRef.current)
      }
    }
  }, [canvasContainer])

  // ✅ RENDU CONDITIONNEL
  if (!isEditing || !isInitialized || !editorContainerRef.current) {
    return null
  }

  // ✅ PORTAL VERS LE CONTAINER CANVAS CRÉÉ
  return ReactDOM.createPortal(
    <EditorContent editor={editor} />,
    editorContainerRef.current
  )
}
